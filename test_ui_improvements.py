import requests
import tempfile
import os
import json

def test_ui_backend_integration():
    """Test that the backend still works correctly with the new UI."""
    print("🧪 Testing UI/Backend Integration...")
    
    # Login
    login_data = {'username': 'gabriel', 'password': 'gabriel123'}
    response = requests.post('http://localhost:5000/api/auth/login', json=login_data, timeout=10)
    
    if response.status_code != 200:
        print('❌ Login failed')
        return False
    
    token = response.json().get('token')
    headers = {'Authorization': f'Bearer {token}'}
    print('✅ Login successful')
    
    # Test 1: Upload a document
    print('\n📄 Testing document upload...')
    test_content = """
UI/UX Test Document

This document tests the new improved Document Management interface.

Features tested:
- Responsive card layout
- Improved delete confirmation modal
- Better file type icons and badges
- Enhanced upload interface with drag-and-drop styling
- Mobile-friendly responsive design

Medical Information:
- Patient: Gabriel
- Test Type: UI/UX Validation
- Status: Testing new interface improvements
- Notes: Document should appear with proper styling and icons

This document validates that the backend integration remains intact
after the frontend UI improvements.
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # Upload
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('ui_test_document.txt', f, 'text/plain')}
            response = requests.post('http://localhost:5000/api/documents/upload', 
                                   files=files, headers=headers, timeout=60)
        
        if response.status_code == 200:
            print('✅ Document upload successful')
            upload_result = response.json()
            print(f'   📄 Document ID: {upload_result.get("document_id", "Unknown")}')
        else:
            print(f'❌ Upload failed: {response.status_code}')
            return False
            
    finally:
        os.unlink(temp_file_path)
    
    # Test 2: List documents
    print('\n📋 Testing document listing...')
    response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
    
    if response.status_code == 200:
        documents = response.json().get('documents', [])
        print(f'✅ Found {len(documents)} documents:')
        
        ui_test_doc = None
        for doc in documents:
            filename = doc.get('filename', 'Unknown')
            print(f'   📄 {filename}')
            if filename == 'ui_test_document.txt':
                ui_test_doc = doc
        
        if ui_test_doc:
            print('✅ UI test document found in list')
        else:
            print('❌ UI test document not found')
            return False
    else:
        print(f'❌ Document listing failed: {response.status_code}')
        return False
    
    # Test 3: Download document
    print('\n⬇️  Testing document download...')
    download_response = requests.get('http://localhost:5000/api/documents/download/ui_test_document.txt', 
                                   headers=headers, timeout=10)
    
    if download_response.status_code == 200:
        print('✅ Document download successful')
        print(f'   📊 Size: {len(download_response.content)} bytes')
        print(f'   🏷️  Content-Type: {download_response.headers.get("Content-Type", "Unknown")}')
    else:
        print(f'❌ Download failed: {download_response.status_code}')
        return False
    
    # Test 4: Delete document
    print('\n🗑️  Testing document deletion...')
    delete_response = requests.delete('http://localhost:5000/api/documents/delete/ui_test_document.txt', 
                                    headers=headers, timeout=10)
    
    if delete_response.status_code == 200:
        print('✅ Document deletion successful')
        delete_result = delete_response.json()
        print(f'   📝 Response: {delete_result.get("message", "Unknown")}')
        
        # Verify deletion
        response = requests.get('http://localhost:5000/api/documents/list', headers=headers, timeout=10)
        if response.status_code == 200:
            documents = response.json().get('documents', [])
            remaining_filenames = [doc.get('filename') for doc in documents]
            if 'ui_test_document.txt' not in remaining_filenames:
                print('✅ Document successfully removed from list')
            else:
                print('❌ Document still in list after deletion')
                return False
    else:
        print(f'❌ Deletion failed: {delete_response.status_code}')
        return False
    
    return True

def main():
    """Main test function."""
    print("🎨 Document Management UI/UX Improvements Test\n")
    print("Testing backend integration after UI improvements...\n")
    
    success = test_ui_backend_integration()
    
    if success:
        print("\n🎉 All backend integration tests PASSED!")
        print("\n📋 UI/UX Improvements Summary:")
        print("✅ Responsive card-based layout for document list")
        print("✅ Improved delete confirmation modal with proper containment")
        print("✅ Enhanced upload interface with drag-and-drop styling")
        print("✅ Better file type icons and badges")
        print("✅ Mobile-friendly responsive design")
        print("✅ Consistent color theming and spacing")
        print("✅ Backend functionality remains intact")
        
        print("\n🌐 Frontend Testing Instructions:")
        print("1. Open http://localhost:3000")
        print("2. Login as gabriel/gabriel123")
        print("3. Navigate to Document Management")
        print("4. Test both 'My Documents' and 'Upload New' tabs")
        print("5. Verify responsive design on different screen sizes")
        print("6. Test delete confirmation modal (should be properly contained)")
        print("7. Upload a document and verify it appears with proper styling")
    else:
        print("\n💥 Some tests failed!")
        print("Check the backend logs for any issues.")

if __name__ == "__main__":
    main()
