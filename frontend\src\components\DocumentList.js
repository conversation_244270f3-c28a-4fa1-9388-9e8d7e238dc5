import { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  Card,
  CardBody,
  Badge,
  Flex,
  useColorModeValue,
  SimpleGrid,
  Stack,
  Divider,
} from '@chakra-ui/react';
import { FiDownload, FiTrash2, FiFile, FiRefreshCw, FiFileText, FiImage } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';

// Helper function to get file icon based on file type
const getFileIcon = (filename) => {
  const extension = filename.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'pdf':
      return FiFile;
    case 'txt':
      return FiFileText;
    case 'jpg':
    case 'jpeg':
    case 'png':
      return FiImage;
    case 'doc':
    case 'docx':
      return FiFileText;
    default:
      return FiFile;
  }
};

// Helper function to get file type badge color
const getFileTypeBadge = (filename) => {
  const extension = filename.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'pdf':
      return { color: 'red', label: 'PDF' };
    case 'txt':
      return { color: 'blue', label: 'TXT' };
    case 'jpg':
    case 'jpeg':
    case 'png':
      return { color: 'green', label: 'IMG' };
    case 'doc':
    case 'docx':
      return { color: 'purple', label: 'DOC' };
    default:
      return { color: 'gray', label: 'FILE' };
  }
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown size';
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const DocumentList = ({ onDocumentDeleted }) => {
  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const { getToken } = useAuth();

  // Color mode values for better theming
  const cardBg = useColorModeValue('white', 'gray.800');
  const cardBorder = useColorModeValue('gray.200', 'gray.600');
  const cardHoverBg = useColorModeValue('gray.50', 'gray.700');
  const textSecondary = useColorModeValue('gray.600', 'gray.400');
  const emptyStateBg = useColorModeValue('gray.50', 'gray.700');

  // Modal-specific color mode values
  const modalBorderColor = useColorModeValue('gray.100', 'gray.700');
  const iconContainerBg = useColorModeValue('red.50', 'red.900');
  const iconContainerBorder = useColorModeValue('red.100', 'red.800');
  const headerTextColor = useColorModeValue('gray.900', 'white');
  const cancelButtonHoverBg = useColorModeValue('gray.100', 'gray.700');

  const fetchDocuments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const response = await api.get('/documents/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      setDocuments(response.data.documents || []);
    } catch (err) {
      console.error('Error fetching documents:', err);
      setError('Failed to load documents. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const handleDownload = async (filename) => {
    try {
      const token = await getToken();

      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = `${api.defaults.baseURL}/documents/download/${filename}`;
      link.setAttribute('download', filename);
      link.setAttribute('target', '_blank');

      // Add authorization header via fetch
      fetch(link.href, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.blob())
      .then(blob => {
        const url = window.URL.createObjectURL(blob);
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      });
    } catch (err) {
      console.error('Error downloading document:', err);
      toast({
        title: 'Download failed',
        description: 'Failed to download the document',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const confirmDelete = (document) => {
    setSelectedDocument(document);
    onOpen();
  };

  const handleDelete = async () => {
    if (!selectedDocument) return;

    try {
      const token = await getToken();
      await api.delete(`/documents/delete/${selectedDocument.filename}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Remove the document from the list
      setDocuments(documents.filter(doc => doc.filename !== selectedDocument.filename));

      toast({
        title: 'Document deleted',
        description: 'The document has been successfully deleted',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Call the callback if provided
      if (onDocumentDeleted) {
        onDocumentDeleted(selectedDocument);
      }
    } catch (err) {
      console.error('Error deleting document:', err);
      toast({
        title: 'Delete failed',
        description: err.response?.data?.error || 'Failed to delete the document',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      onClose();
      setSelectedDocument(null);
    }
  };

  if (isLoading) {
    return (
      <Box textAlign="center" py={10}>
        <Spinner size="xl" />
        <Text mt={4}>Loading documents...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        <AlertTitle mr={2}>Error!</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header Section */}
      <Flex
        justifyContent="space-between"
        alignItems={{ base: "flex-start", md: "center" }}
        direction={{ base: "column", md: "row" }}
        gap={4}
        mb={6}
      >
        <Box>
          <Heading size="lg" mb={2}>Your Documents</Heading>
          <Text color={textSecondary} fontSize="sm">
            {documents.length} {documents.length === 1 ? 'document' : 'documents'} uploaded
          </Text>
        </Box>
        <Button
          leftIcon={<FiRefreshCw />}
          size="md"
          onClick={fetchDocuments}
          colorScheme="blue"
          variant="outline"
          isLoading={isLoading}
          loadingText="Refreshing"
        >
          Refresh
        </Button>
      </Flex>

      {/* Empty State */}
      {documents.length === 0 ? (
        <Card bg={emptyStateBg} borderStyle="dashed" borderWidth="2px" borderColor={cardBorder}>
          <CardBody textAlign="center" py={12}>
            <Icon as={FiFile} boxSize={12} color={textSecondary} mb={4} />
            <Heading size="md" mb={2} color={textSecondary}>No documents found</Heading>
            <Text color={textSecondary} mb={4}>
              Upload your first document to get started with AI-powered health insights
            </Text>
          </CardBody>
        </Card>
      ) : (
        /* Document Grid - Responsive Layout */
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={4}>
          {documents.map((doc) => {
            const fileIcon = getFileIcon(doc.filename);
            const fileBadge = getFileTypeBadge(doc.filename);

            return (
              <Card
                key={doc.id || doc.filename}
                bg={cardBg}
                borderColor={cardBorder}
                borderWidth="1px"
                _hover={{
                  bg: cardHoverBg,
                  transform: "translateY(-2px)",
                  shadow: "md"
                }}
                transition="all 0.2s"
                cursor="pointer"
              >
                <CardBody p={6}>
                  <Stack spacing={4}>
                    {/* File Header */}
                    <Flex alignItems="flex-start" justifyContent="space-between">
                      <HStack spacing={3} flex={1} minW={0}>
                        <Icon
                          as={fileIcon}
                          color={`${fileBadge.color}.500`}
                          boxSize={6}
                          flexShrink={0}
                        />
                        <Box flex={1} minW={0}>
                          <Text
                            fontWeight="semibold"
                            fontSize="md"
                            noOfLines={1}
                            title={doc.filename}
                          >
                            {doc.filename}
                          </Text>
                          <HStack spacing={2} mt={1}>
                            <Badge
                              colorScheme={fileBadge.color}
                              size="sm"
                              variant="subtle"
                            >
                              {fileBadge.label}
                            </Badge>
                            <Text fontSize="xs" color={textSecondary}>
                              {doc.metadata?.content_type || 'Document'}
                            </Text>
                          </HStack>
                        </Box>
                      </HStack>
                    </Flex>

                    <Divider />

                    {/* Action Buttons */}
                    <Flex
                      direction={{ base: "column", sm: "row" }}
                      gap={2}
                      justifyContent="flex-end"
                    >
                      <Button
                        size="sm"
                        leftIcon={<FiDownload />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(doc.filename);
                        }}
                        colorScheme="blue"
                        variant="ghost"
                        flex={{ base: 1, sm: "none" }}
                      >
                        Download
                      </Button>
                      <Button
                        size="sm"
                        leftIcon={<FiTrash2 />}
                        onClick={(e) => {
                          e.stopPropagation();
                          confirmDelete(doc);
                        }}
                        colorScheme="red"
                        variant="ghost"
                        flex={{ base: 1, sm: "none" }}
                      >
                        Delete
                      </Button>
                    </Flex>
                  </Stack>
                </CardBody>
              </Card>
            );
          })}
        </SimpleGrid>
      )}

      {/* Enhanced Delete Confirmation Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        isCentered
        motionPreset="slideInBottom"
        size={{ base: "sm", md: "md" }}
      >
        <ModalOverlay
          bg="blackAlpha.600"
          backdropFilter="blur(10px)"
        />
        <ModalContent
          mx={4}
          bg={cardBg}
          borderRadius="2xl"
          shadow="2xl"
          borderWidth="1px"
          borderColor={modalBorderColor}
          overflow="hidden"
        >
          <ModalHeader pb={3} pt={6} px={6}>
            <HStack spacing={3} align="center">
              <Box
                p={2}
                borderRadius="lg"
                bg={iconContainerBg}
                border="1px solid"
                borderColor={iconContainerBorder}
              >
                <Icon as={FiTrash2} color="red.500" boxSize={5} />
              </Box>
              <Box>
                <Text fontSize="lg" fontWeight="semibold" color={headerTextColor}>
                  Confirm Deletion
                </Text>
                <Text fontSize="sm" color={textSecondary} mt={1}>
                  This action cannot be undone
                </Text>
              </Box>
            </HStack>
          </ModalHeader>

          <ModalBody pb={6} px={6}>
            <VStack spacing={5} align="stretch">
              <Text color={textSecondary} fontSize="md" lineHeight="1.6">
                Are you sure you want to delete this document? All associated data will be permanently removed.
              </Text>

              {selectedDocument && (
                <Card
                  bg={emptyStateBg}
                  borderWidth="1px"
                  borderColor={cardBorder}
                  borderRadius="xl"
                  shadow="sm"
                  transition="all 0.2s"
                  _hover={{ shadow: "md" }}
                >
                  <CardBody p={5}>
                    <HStack spacing={4}>
                      <Box
                        p={2}
                        borderRadius="lg"
                        bg={`${getFileTypeBadge(selectedDocument.filename).color}.50`}
                        border="1px solid"
                        borderColor={`${getFileTypeBadge(selectedDocument.filename).color}.100`}
                        _dark={{
                          bg: `${getFileTypeBadge(selectedDocument.filename).color}.900`,
                          borderColor: `${getFileTypeBadge(selectedDocument.filename).color}.800`
                        }}
                      >
                        <Icon
                          as={getFileIcon(selectedDocument.filename)}
                          color={`${getFileTypeBadge(selectedDocument.filename).color}.500`}
                          boxSize={5}
                        />
                      </Box>
                      <Box flex={1} minW={0}>
                        <Text fontWeight="semibold" noOfLines={1} fontSize="md">
                          {selectedDocument.filename}
                        </Text>
                        <Text fontSize="sm" color={textSecondary} mt={1}>
                          {selectedDocument.metadata?.content_type || 'Document'}
                        </Text>
                      </Box>
                    </HStack>
                  </CardBody>
                </Card>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter pt={0} pb={6} px={6}>
            <Stack
              direction={{ base: "column", sm: "row" }}
              spacing={3}
              width="full"
              justifyContent="flex-end"
            >
              <Button
                variant="ghost"
                onClick={onClose}
                flex={{ base: 1, sm: "none" }}
                size="md"
                borderRadius="lg"
                _hover={{ bg: cancelButtonHoverBg }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="red"
                onClick={handleDelete}
                leftIcon={<FiTrash2 />}
                flex={{ base: 1, sm: "none" }}
                isLoading={false}
                loadingText="Deleting..."
                size="md"
                borderRadius="lg"
                shadow="sm"
                _hover={{ shadow: "md", transform: "translateY(-1px)" }}
                transition="all 0.2s"
              >
                Delete Document
              </Button>
            </Stack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default DocumentList;
